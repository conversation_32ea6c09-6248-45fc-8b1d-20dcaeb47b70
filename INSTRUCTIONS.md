# 嵌入式两片版本预览 URL 传参使用说明

## 1. URL 结构

```js
**基础 URL：http://127.0.0.1:<端口>/index.html/#/?url=<资源路径>&language=<语言>

- `url`（必填）：表示要加载的资源路径，需使用**绝对路径**。
- `language`（可选）：用于设置界面语言，支持：
  - 简体中文（`zh-CN`）
  - 英文（`en-US`）
  - 繁体中文（`zh-TW`）
```

---

## 2. 参数说明

| 参数名     | 类型   | 是否必填 | 说明                                                          |
| ---------- | ------ | -------- | ------------------------------------------------------------- |
| `url`      | string | ✅ 是    | 指定要加载的资源路径，需使用 URL 编码处理特殊字符（如中文）。 |
| `language` | string | ❌ 否    | 设定界面语言。不传时使用默认语言（通常是 `zh-CN`）。          |

---

## 3. 前端使用方式（示例）

```js
window.open(
  'http://127.0.0.1:7012/index.html/#/?url=/切片/JPEG400.sdpc',
  'view'
);

方法说明：window.open(url, target, features)
	•	url（必填）：要打开的页面 URL，支持查询参数传递信息。
	•	target（可选）：窗口名称或特殊关键字，如：
	•	_blank（新窗口）
	•	_self（当前窗口）
	•	view（指定名称的窗口，若已存在则复用）
	•	features（可选）：窗口的特性（如宽高、是否允许滚动等）。
```

---

## 4. 示例带语言设置

```js
window.open('http://127.0.0.1:7012/index.html/#/?url=/切片/JPEG400.sdpc&language=en-US', '_blank');
```
