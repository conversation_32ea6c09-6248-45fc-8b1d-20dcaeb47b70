<template>
  <div class="h-[calc(100vh-80px)] flex-c">
    <div class="flex flex-col items-center">
      <a-empty>
        <template #image>
          <div
            class="w-[260px] h-[140px] bg-[url('@/assets/images/404.png')] bg-no-repeat bg-center"
          ></div>
        </template>
        抱歉，页面不见了～
      </a-empty>
      <a-button type="primary" @click="router.go(-1)">返回</a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
  const router = useRouter();
</script>
<style scoped lang="scss"></style>
