<template>
  <div
    class="absolute top-0 left-0 w-full h-[78px] bg-color-bg-1 z-[110] opacity-80 shadow-box-shadow"
  ></div>
  <div
    class="
      absolute
      top-0
      left-0
      w-full
      flex
      justify-between
      text-color-text-1
      h-[78px]
      z-[111]
      opacity-1
      select-none
    "
  >
    <div class="w-full flex flex-row justify-end items-center pl-3 text-[16px]">
      <div
        class="px-[18px] button-color shrink-0 cursor-context-menu active-button"
        @click="zoomTo(imageBrower?.minZoom || 1)"
      >
        {{ $t('index.ZzCFEccP') }}
      </div>
      <div
        class="w-[74px] ml-3 button-color cursor-context-menu text-center"
        :class="zoom == 4 ? 'bg-theme-color text-color-bg-1' : ''"
        @click="zoomTo(4)"
      >
        4X
      </div>
      <div
        class="w-[74px] ml-3 button-color cursor-context-menu text-center"
        :class="zoom == 10 ? 'bg-theme-color text-color-bg-1' : ''"
        @click="zoomTo(10)"
      >
        10X
      </div>
      <div
        class="w-[74px] ml-3 button-color cursor-context-menu text-center"
        :class="zoom == 20 ? 'bg-theme-color text-color-bg-1' : ''"
        @click="zoomTo(20)"
      >
        20X
      </div>
      <div
        class="w-[74px] ml-3 button-color cursor-context-menu text-center"
        :class="zoom == 40 ? 'bg-theme-color text-color-bg-1' : ''"
        @click="zoomTo(40)"
      >
        40X
      </div>
      <div
        class="w-[74px] ml-3 button-color cursor-context-menu text-center"
        :class="zoom == 80 ? 'bg-theme-color text-color-bg-1' : ''"
        @click="zoomTo(80)"
      >
        80X
      </div>
      <div class="w-[74px] ml-3 text-center button-color cursor-context-menu">{{ zoom }}X</div>
      <!-- <div class="ml-3 flex-c button-color px-[22px] active-button" @click="message">
        <SvgIcon :size="28" iconName="icon-yuepiangongjulan_qiepianxinxi_xuanzhong" />
      </div> -->
      <div class="ml-3 flex-c button-color px-[22px] active-button" @click="zoomTo(zoom / 1.3)">
        <SvgIcon :size="28" iconName="icon-yuepiangongjulan_suoxiao_xuanzhong" />
      </div>
      <!-- 放大 -->
      <div class="ml-3 flex-c button-color px-[22px] active-button" @click="zoomTo(zoom * 1.3)">
        <SvgIcon :size="28" iconName="icon-yuepiangongjulan_fangda_xuanzhong" />
      </div>

      <!-- 颜色调节 -->
      <div
        class="ml-3 flex-c button-color px-[22px]"
        :class="marker.colorAdjustShow ? 'bg-theme-color text-color-bg-1' : ''"
        @click="setPopoverVisible"
      >
        <SvgIcon :size="28" iconName="icon-yuepiangongjulan_yansetiaojie_xuanzhong" />
      </div>
      <!-- 翻转 -->
      <div class="ml-3 flex-c button-color px-[22px] active-button" @click="filp">
        <SvgIcon :size="28" iconName="icon-yuepiangongjulan_jingxiang_xuanzhong" />
      </div>

      <div
        class="ml-3 mr-[8px] h-full flex-c bg-[#F4F7FD] rounded-[4px] px-[22px] active-button"
        :class="marker.markerOpen ? 'bg-theme-color text-color-bg-1' : ''"
        @click="markClick"
      >
        <div>
          <div class="flex-c"><SvgIcon :size="28" iconName="icon-biaozhuliebiao_xuanzhong" /></div>
          <div>{{ $t('topTool.KcsaSGRS') }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { sdpcinfo } from '@/utils/sdpc';
  import { useI18n } from 'vue-i18n';
  import useStore from '@/store';
  const { t } = useI18n();

  const props = defineProps({
    imageBrower: {
      type: Object as PropType<any>,
      required: true,
    },
    slice: {
      type: Object as PropType<sdpcinfo | undefined>,
      required: true,
    },
  });
  const { marker } = useStore();
  //切片信息
  const visible = defineModel<boolean>('visible');
  const filp = () => {
    if (!props.slice?.sliceServerId) return;
    props.imageBrower?.flipViewer();
    const markerOverlay = document.getElementById('konva-overlay');
    if (markerOverlay) {
      markerOverlay.style.transform = props.imageBrower.isFilp ? 'scaleX(-1)' : '';
    }
  };
  //zoom的值
  const zoom = computed(() => {
    if (!props.imageBrower) return 1;
    return props.imageBrower?.zoom;
  });
  //缩放
  const zoomTo = (zoom: number) => {
    if (!props.slice?.sliceServerId) return;
    if (isNaN(zoom as any)) {
      zoom = 1;
    }
    zoom =
      (props.imageBrower && zoom < props.imageBrower?.minZoom
        ? props.imageBrower?.minZoom
        : zoom > props.imageBrower!.maxZoom
        ? props.imageBrower?.maxZoom
        : zoom) || 0;
    props.imageBrower?.zoomTo(zoom as number);
    setTimeout(() => {
      props.imageBrower?.setScaleRuler();
    }, 100);
  };

  const message = () => {
    if (props.imageBrower?.viewer) {
      visible.value = true;
    }
  };
  const setPopoverVisible = () => {
    marker.colorAdjustShow = !marker.colorAdjustShow;
    marker.markerOpen = false;
    marker.markerIsOpen = false;
  };
  const markClick = () => {
    marker.markerOpen = !marker.markerOpen;
    marker.colorAdjustShow = false;
    marker.markerIsOpen = false;
  };
</script>
<style lang="scss" scoped></style>
