<template>
  <div
    v-if="marker.colorAdjustShow"
    class="
      absolute
      top-[78px]
      right-[35px]
      px-[10px]
      py-[12px]
      w-[470px]
      bg-color-bg-1
      text-[16px]
      rounded-[4px]
      z-[1000]
      select-none
    "
  >
    <template v-for="(item, index) in correctionColor" :key="item.type">
      <div class="flex items-center mb-4" id="correctionColor">
        <div class="ml-1 flex flex-row items-center w-[110px]">
          <SvgIcon class="mr-2" :iconName="item.icon" />
          {{ $t(item.name) }}
        </div>
        <div>
          <TheSlider
            v-if="item.type !== 'gamma'"
            class="!w-[160px]"
            v-model="correctionColor[index].value"
            :max="correctionColor[index].max || 100"
            :min="correctionColor[index].min"
            @change="(val) => (correctionColor[index].value = Math.floor(val as number) as number)"
          />
          <TheSlider
            v-else
            class="!w-[160px]"
            v-model="correctionColor[index].value"
            :max="correctionColor[index].max"
            :min="correctionColor[index].min"
            :step="correctionColor[index].step"
            :initial="1"
            @change="(val) => (correctionColor[index].value =  val as number)"
          />
        </div>
        <div
          class="
            ml-[30px]
            flex-c
            min-w-[48px]
            h-[40px]
            bg-[#F4F7FD]
            rounded-[4px]
            text-[32px] text-[#7B7D83]
            shadow-box-shadow-1
          "
          @click="subtract(correctionColor[index])"
        >
          <SvgIcon :size="28" iconName="icon-Frame1" />
        </div>
        <div class="min-w-[49px] text-center text-[16px]">
          {{ correctionColor[index].value }}
        </div>
        <div
          class="
            flex-c
            min-w-[48px]
            h-[40px]
            bg-[#F4F7FD]
            rounded-[4px]
            text-[32px] text-[#7B7D83]
            shadow-box-shadow-1
          "
          @click="add(correctionColor[index])"
        >
          <SvgIcon :size="28" iconName="icon-Frame-2" />
        </div>
      </div>
    </template>
    <div class="mt-[30px] w-full flex flex-row justify-center">
      <a-button
        class="mr-3 w-full h-[48px] text-[18px]"
        @click="correctionColor = initCorrectionColor()"
      >
        {{ $t('index.MNFcdSKT') }}
      </a-button>
      <a-button
        type="primary"
        class="w-full h-[48px] text-[18px]"
        @click="marker.colorAdjustShow = false"
      >
        {{ $t('index.REhitKma') }}
      </a-button>
    </div>
  </div>
</template>
<script setup lang="ts">
  import md5 from 'md5';
  import { sdpcinfo } from '@/utils/sdpc';
  import FilterPlugin from 'filterplugin';
  import useStore from '@/store';
  const props = defineProps({
    imageBrower: {
      type: Object as PropType<any>,
      required: true,
    },
    slice: {
      type: Object as PropType<sdpcinfo | undefined>,
      required: true,
    },
  });
  const { marker } = useStore();
  const initCorrectionColor = () => {
    return [
      {
        name: 'index.n4fTaQfw',
        type: 'red',
        color: 'red',
        value: 0,
        max: 100,
        min: -100,
        icon: 'icon-a-yuanxing21',
      },
      {
        name: 'index.2HPGCaiJ',
        type: 'green',
        color: 'green',
        value: 0,
        max: 100,
        min: -100,
        icon: 'icon-a-yuanxing21-2',
      },
      {
        name: 'index.x6m78Ktz',
        type: 'blue',
        color: 'blue',
        value: 0,
        max: 100,
        min: -100,
        icon: 'icon-a-yuanxing21-3',
      },
      {
        name: 'index.EJ4wyXR7',
        type: 'brightness',
        color: 'brightness',
        value: 0,
        max: 255,
        min: -255,
        icon: 'icon-a-zujian5',
      },
      {
        name: 'index.R2hQHfJD',
        type: 'contrast',
        color: 'contrast',
        value: 0,
        max: 100,
        min: -100,
        icon: 'icon-a-zujian9',
      },
      {
        name: 'index.tSPsMRYt',
        type: 'gamma',
        color: 'gamma',
        step: 0.1,
        max: 5.0,
        min: 0.0,
        value: 1,
        icon: 'icon-a-rongqi2',
      },
    ];
  };
  //颜色调节
  const correctionColor = ref(initCorrectionColor());
  const subtract = (val: any) => {
    if (val.type === 'gamma') {
      if (val.value > val.min) {
        val.value = Number((val.value - val.step).toFixed(1));
      }
    } else {
      if (val.value > val.min) {
        val.value--;
      }
    }
  };
  const add = (val: any) => {
    if (val.type === 'gamma') {
      if (val.value < val.max) {
        val.value = Number((val.value + val.step).toFixed(1));
      }
    } else {
      if (val.value < val.max) {
        val.value++;
      }
    }
  };
  //调节颜色
  watch(
    () => correctionColor.value,
    (n, o) => {
      if (
        !props.slice?.sliceInfo ||
        (props.slice?.sliceInfo?.dcmExtraInfo && props.slice?.sliceInfo?.dcmExtraInfo?.dcmType)
      )
        return;
      const md5List = n.map((item) => {
        return item.value;
      });
      const colorMd5 = `red=${md5List[0]},green=${md5List[1]},blue=${md5List[2]},brightness=${md5List[3]},contrast=${md5List[4]},gamma=${md5List[5]},colorType=1`;
      debounce(async () => {
        await props.slice?.colorChange(md5List);
        props.imageBrower?.refresh(md5(colorMd5));
      }, 50)();

      // const md5List = n.map((item) => {
      //   return item.value;
      // });
      // props.imageBrower.viewer.setFilterOptions({
      //   filters: {
      //     processors: [
      //       FilterPlugin.Filters.ADJUST(
      //         md5List[5],
      //         md5List[4],
      //         md5List[3],
      //         md5List[0],
      //         md5List[1],
      //         md5List[2]
      //       ),
      //     ],
      //   },
      // });
    },
    {
      deep: true,
    }
  );
  //防抖
  const timeout: any = ref(null);
  const debounce: (doSomeThing: any, time: number) => any = (doSomeThing, time) => {
    return () => {
      if (timeout.value) {
        clearTimeout(timeout.value);
      }
      timeout.value = setTimeout(() => {
        doSomeThing();
      }, time);
    };
  };
</script>
<style lang="scss" scoped></style>
