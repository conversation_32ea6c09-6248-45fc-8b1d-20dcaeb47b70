<template>
  <a-modal v-model:visible="markerSetShow">
    <template #title>
      <div class="text-[22px] font-bold">{{ $t('markerSet.7r5zfG85') }}</div>
    </template>
    <div class="p-[16px] text-[18px] bg-[#F4F7FD]">
      <div class="text-[18px] text-[#313033] font-bold mb-[15px]">
        {{ $t('markerSet.ZypRsQyd') }}
      </div>
      <TheSlider class="w-full" v-model="lineWidth" :max="10" :min="1" :step="1" />
      <div class="text-[18px] text-[#313033] font-bold mt-[30px]">
        {{ $t('markerSet.M5XS7xNK') }}
      </div>
      <div class="mt-[12px] flex justify-between">
        <div
          v-for="item in colorList"
          :key="item.color"
          class="w-[32px] h-[32px] rounded-[4px]"
          :class="[item.active ? 'shadow-box-shadow-2' : '']"
          :style="{ backgroundColor: item.color, transform: `scale(${item.active ? 1.2 : 1})` }"
          @click="colorClick(item)"
        ></div>
      </div>
    </div>
    <template #footer>
      <div class="flex justify-center">
        <a-button class="w-full h-[54px] text-[18px]" @click="markerSetShow = false">
          {{ $t('markerSet.f3fMiCcP') }}
        </a-button>
        <a-button class="ml-[17px] w-full h-[54px] text-[18px]" type="primary" @click="submit">
          {{ $t('markerSet.MHecB4dZ') }}
        </a-button>
      </div>
    </template>
  </a-modal>
</template>
<script setup lang="ts">
  import useStore from '@/store';
  import { useI18n } from 'vue-i18n';
  const { t } = useI18n();

  const { marker } = useStore();

  const markerSetShow = defineModel<boolean>('markerSetShow');

  const lineWidth = ref(marker.lineWidth);

  //当前选中颜色
  const color = ref(marker.lineColor);

  const colorList = reactive([
    {
      color: '#ff0000',
      active: false,
    },
    {
      color: '#7DD707',
      active: false,
    },
    {
      color: '#00B7FF',
      active: false,
    },
    {
      color: '#0022FF',
      active: false,
    },
    {
      color: '#FF00B2',
      active: false,
    },
    {
      color: '#FFCC00',
      active: false,
    },
    {
      color: '#E100FF',
      active: false,
    },
    {
      color: '#A5A862',
      active: false,
    },
    {
      color: '#F87D16',
      active: false,
    },
  ]);

  const colorClick = (item: any) => {
    colorList.forEach((item) => {
      item.active = false;
    });
    item.active = true;
    color.value = item.color;
  };

  const submit = () => {
    markerSetShow.value = false;
    marker.lineWidth = lineWidth.value;
    marker.lineColor = color.value;
  };

  watch(
    () => markerSetShow.value,
    (newValue) => {
      if (newValue) {
        colorList.forEach((item) => {
          item.active = false;
        });
        colorList.forEach((item) => {
          if (item.color === marker.lineColor) {
            item.active = true;
          }
        });
      }
    }
  );
</script>
<style lang="scss" scoped></style>
