<template>
  <div class="absolute flex top-[80px] right-[8px] z-[999]">
    <div
      v-if="marker.markerIsOpen"
      class="mt-1 mr-[9px] w-[294px] h-[578px] bg-[#ffffff] rounded-[4px] shadow-box-shadow-2"
    >
      <div
        class="
          flex
          justify-between
          px-[8px]
          h-[48px]
          leading-[48px]
          text-[20px] text-[#343840]
          font-bold
          border-b-[1px] border-[#E0E8FA]
        "
      >
        <div>{{ $t('marker.5MhS2jZx') }}</div>
        <div>
          <icon-double-right
            :size="24"
            class="text-[#313033]"
            @click="marker.markerIsOpen = false"
          />
        </div>
      </div>
      <div class="pb-[10px] h-[525px] overflow-y-auto">
        <div
          v-for="item in markerList"
          :key="item.id"
          class="marker-item mt-[10px] mx-[10px] border-[1px] border-[#E0E8FA]"
          :class="{ active: item.id === drawSelectId }"
        >
          <div
            class="flex justify-between py-[7px] px-[5px] border-b-[1px] border-dashed"
            :class="[item.id === drawSelectId ? 'border-theme-color' : '']"
          >
            <div>
              <SvgIcon :size="24" color="#7B7D83" :iconName="item.icon" />
            </div>
            <div
              v-if="!['flag', 'ring'].includes(item.name as string)"
              class="w-[24px] h-[24px] rounded-[4px]"
              :style="{ backgroundColor: item.labelColor }"
            ></div>
          </div>
          <div class="py-[7px] flex justify-around">
            <div class="flex-c w-[58px] h-[34px] bg-[#F4F7FD] rounded-[4px]">
              <SvgIcon
                :size="24"
                color="#7B7D83"
                iconName="icon-biaozhugongjulan_dingweibiaozhu_xuanzhong"
                @click="positionMarker(item)"
              />
            </div>
            <div class="flex-c w-[58px] h-[34px] bg-[#F4F7FD] rounded-[4px]">
              <SvgIcon
                v-show="markerIsVisible(item)"
                :size="24"
                color="#7B7D83"
                iconName="icon-mima_biyan1"
                @click="hideMarker(item)"
              />
              <SvgIcon
                v-show="!markerIsVisible(item)"
                :size="24"
                color="#7B7D83"
                iconName="icon-mima_zhengyan"
                @click="showMarker(item)"
              />
            </div>
            <div
              class="relative flex-c w-[58px] h-[34px] bg-[#F4F7FD] rounded-[4px]"
              :class="[editMarkerId === item.id ? 'bg-theme-color' : '']"
            >
              <SvgIcon
                :size="24"
                :color="editMarkerId === item.id ? '#ffffff' : '#7B7D83'"
                iconName="icon-biaozhugongjulan_bianjibeizhu_xuanzhong"
                @click="editMarker(item)"
              />
              <div v-if="editMarkerId === item.id" class="pop-confirm right-[-68px]">
                <div class="pop-confirm-s right-[90px]"></div>
                <div class="mb-5">{{ $t('markerSet.ZypRsQyd') }}</div>
                <TheSlider
                  class="!w-[237px]"
                  v-model="lineWidth"
                  :max="10"
                  :min="1"
                  :step="1"
                  @change="lineChange($event, item)"
                />
                <div class="mt-[30px] mb-5">{{ $t('markerSet.M5XS7xNK') }}</div>
                <div class="flex justify-between">
                  <div
                    v-for="val in colorList"
                    :key="val.color"
                    class="w-[32px] h-[32px] rounded-[4px]"
                    :class="[val.active ? 'shadow-box-shadow-2' : '']"
                    :style="{
                      backgroundColor: val.color,
                      transform: `scale(${val.active ? 1.2 : 1})`,
                    }"
                    @click="colorClick(val, item)"
                  ></div>
                </div>
              </div>
            </div>
            <div
              class="relative flex-c w-[58px] h-[34px] bg-[#F4F7FD] rounded-[4px]"
              :class="[deleteConfirmId === item.id ? 'bg-theme-color' : '']"
            >
              <SvgIcon
                :size="24"
                :color="deleteConfirmId === item.id ? '#ffffff' : '#7B7D83'"
                iconName="icon-biaozhugongjulan_shanchubiaozhu_xuanzhong"
                @click="toggleDeleteConfirm(item.id)"
              />
              <div v-if="deleteConfirmId === item.id" class="pop-confirm right-0">
                <div class="pop-confirm-s right-[21px]"></div>
                <div class="mt-[15px] text-center text-[18px] font-medium text-[#313033]">
                  {{ $t('common.deleteConfirm') }}
                </div>
                <div class="mt-[20px] flex justify-around">
                  <a-button
                    @click="cancelDelete"
                    class="w-[117px] h-[42px] rounded-[4px] text-[#1f2329]"
                  >
                    {{ $t('markerSet.f3fMiCcP') }}
                  </a-button>
                  <a-button
                    @click="confirmDelete(item.id)"
                    class="ml-[9px] w-[117px] h-[42px] rounded-[4px]"
                    type="primary"
                  >
                    {{ $t('index.REhitKma') }}
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="marker.markerOpen"
      class="p-[6px] pb-0 w-[72px] bg-[#ffffff] rounded-[4px] shadow-box-shadow-2"
    >
      <div
        class="mb-1 flex-c w-[60px] h-[39px] bg-[#F4F7FD] active:opacity-50 rounded-[4px]"
        @click="marker.markerIsOpen = true"
      >
        <icon-double-left :size="24" class="text-[#313033]" />
      </div>
      <div
        v-for="item in markerType"
        :key="item.type"
        class="mb-2 flex-c w-[60px] h-[60px] bg-[#F4F7FD] rounded-[4px]"
        @click="addMarker(item)"
        :class="[drawType === item.type ? '!bg-theme-color' : '']"
      >
        <SvgIcon
          :size="28"
          :color="drawType === item.type ? '#ffffff' : '#7B7D83'"
          :iconName="item.icon"
        />
      </div>
    </div>
  </div>
  <div
    v-if="tipsIsShow"
    class="
      absolute
      left-1/2
      top-[88px]
      -translate-x-1/2
      text-orange-500
      p-2
      z-50
      flex flex-row
      item-center
      bg-white/75
      shadow-md
    "
  >
    {{ $t('index.EdAW2sfm') }}
  </div>

  <!-- 标注设置 start -->
  <MarkerSet v-model:markerSetShow="markerSetShow" />
  <!-- 标注设置 end -->
</template>
<script setup lang="ts">
  import { SQMarker } from 'sqray-marker';
  import { IconDoubleRight, IconDoubleLeft } from '@arco-design/web-vue/es/icon';
  import MarkerSet from './markerSet.vue';
  import useStore from '@/store';
  import { useI18n } from 'vue-i18n';
  import { sdpcinfo } from '@/utils/sdpc';
  const { t } = useI18n();

  const props = defineProps({
    imageBrower: {
      type: Object,
      required: true,
    },
    slice: {
      type: Object as PropType<sdpcinfo | undefined>,
      required: true,
    },
  });
  const { marker } = useStore();
  //标注工具是否打开
  const sqMarker = ref();
  const drawType = ref('');
  const drawIcon = ref('');
  const markerList = ref<
    {
      id: string;
      labelName?: string;
      labelColor: string;
      labelWidth: string;
      name?: string;
      icon?: string;
    }[]
  >([]);
  const drawSelectId = ref<any>(null);
  const deleteConfirmId = ref<string | null>(null);
  const editMarkerId = ref<string | null>(null);
  const tipsIsShow = ref(false);
  const markerSetShow = ref(false);
  const lineWidth = ref(1);
  const color = ref();
  //定义标注类型数组
  const markerType = reactive([
    {
      icon: 'icon-biaozhugongjulan_zhixian_xuanzhong',
      type: 'line',
    },
    {
      icon: 'icon-biaozhugongjulan_jiantou_xuanzhong-2',
      type: 'arrow',
    },
    {
      icon: 'icon-biaozhugongjulan_fangxing_xuanzhong',
      type: 'rect',
    },
    {
      icon: 'icon-biaozhugongjulan_yuanxing_xuanzhong',
      type: 'circle',
    },
    {
      icon: 'icon-biaozhugongjulan_jiajiao_xuanzhong',
      type: 'angle',
    },
    {
      icon: 'icon-biaozhugongjulan_tongxinyuan_xuanzhong',
      type: 'ring',
    },
    {
      icon: 'icon-biaozhugongjulan_biaoqi_xuanzhong',
      type: 'flag',
    },
    {
      icon: 'icon-biaozhugongjulan_bihequxian_xuanzhong',
      type: 'closedCurve',
    },
    {
      icon: 'icon-caidanlan_shezhi',
      type: 'setting',
    },
  ]);
  const addMarker = (val: { type: string; icon: string }) => {
    if (val.type === 'setting') {
      sqMarker.value.editor.close();
      return (markerSetShow.value = true);
    }
    drawType.value = val.type;
    drawIcon.value = val.icon;
    switch (val.type) {
      case 'line':
        sqMarker.value?.drawLine({
          stroke: marker.lineColor,
          strokeWidth: marker.lineWidth,
        });
        break;
      case 'arrow':
        sqMarker.value?.drawArrow({
          stroke: marker.lineColor,
          strokeWidth: marker.lineWidth,
        });
        break;
      case 'rect':
        sqMarker.value?.drawRect({
          stroke: marker.lineColor,
          strokeWidth: marker.lineWidth,
        });
        break;
      case 'angle':
        sqMarker.value?.drawAngle({
          stroke: marker.lineColor,
          strokeWidth: marker.lineWidth,
        });
        break;
      case 'circle':
        sqMarker.value?.drawCircle({
          stroke: marker.lineColor,
          strokeWidth: marker.lineWidth,
        });
        break;
      case 'ring':
        sqMarker.value?.drawRing({
          stroke: marker.lineColor,
          strokeWidth: marker.lineWidth,
        });
        break;
      case 'flag':
        sqMarker.value?.drawFlag({
          strokeWidth: marker.lineWidth,
        });
        break;
      case 'closedCurve':
        sqMarker.value?.drawClosedCurve({
          stroke: marker.lineColor,
          strokeWidth: marker.lineWidth,
        });
        break;

      default:
        break;
    }
  };
  const positionMarker = (marker: { id: any }) => {
    sqMarker?.value.editor.positioningMark(marker.id);
  };
  const markerIsVisible = (marker: { id: any }) => {
    return sqMarker?.value?.editor.markIsVisible(marker.id);
  };
  const hideMarker = (marker: { id: any }) => {
    sqMarker?.value.editor.hiddenMark(marker.id);
  };
  const showMarker = (marker: { id: any }) => {
    sqMarker?.value.editor.showMark(marker.id);
  };
  const editMarker = (item: {
    [x: string]: string;
    labelWidth: string;
    labelColor: any;
    id: string;
  }) => {
    if (['flag', 'ring'].includes(item.name)) return;
    lineWidth.value = Number(item.labelWidth);
    color.value = item.labelColor;
    colorList.forEach((item) => {
      item.active = item.color === color.value;
    });
    editMarkerId.value = editMarkerId.value === item.id ? null : item.id;
    deleteConfirmId.value = null;
  };
  const delMarker = (marker: { id: string }) => {
    sqMarker.value?.editor.destroyMark(marker.id);
    sqMarker.value?.label.delLabel(marker);
    markerList.value = markerList.value.filter((item) => item.id !== marker.id);
  };

  const toggleDeleteConfirm = (id: string) => {
    deleteConfirmId.value = deleteConfirmId.value === id ? null : id;
    editMarkerId.value = null;
  };

  const cancelDelete = () => {
    deleteConfirmId.value = null;
  };

  const confirmDelete = (id: string) => {
    delMarker({ id });
    deleteConfirmId.value = null;
  };

  const handleDrawEnd = (node: any) => {
    console.log(node, '+++++');

    const markerId = node.getId?.() || node.id || node._id;
    markerList.value.push({
      labelName: node.attrs.name,
      labelColor: marker.lineColor,
      labelWidth: marker.lineWidth + '',
      id: markerId,
      name: node.attrs.name,
      icon: drawIcon.value,
    });
    sqMarker.value.label.addLabel(node);
  };

  //线宽调整
  const lineChange = (val: any, item: { labelWidth: string }) => {
    item.labelWidth = val;
    sqMarker.value?.editor.setAttr(editMarkerId.value, {
      stroke: color.value,
      strokeWidth: val,
    });
  };
  const colorList = reactive([
    {
      color: '#ff0000',
      active: false,
    },
    {
      color: '#7DD707',
      active: false,
    },
    {
      color: '#00B7FF',
      active: false,
    },
    {
      color: '#0022FF',
      active: false,
    },
    {
      color: '#FF00B2',
      active: false,
    },
    {
      color: '#FFCC00',
      active: false,
    },
  ]);
  const colorClick = (item: any, val: { labelColor: string }) => {
    colorList.forEach((item) => {
      item.active = false;
    });
    item.active = true;
    color.value = item.color;
    val.labelColor = item.color;
    sqMarker.value?.editor.setAttr(editMarkerId.value, {
      stroke: color.value,
      strokeWidth: lineWidth.value,
    });
  };

  onMounted(async () => {
    sqMarker.value = new SQMarker(props.imageBrower.viewer);
    sqMarker.value?.editor?.drawer?.setDrawEndCallback(handleDrawEnd);
    sqMarker.value.editor.startCallback(() => {
      tipsIsShow.value = true;
    });
    sqMarker.value.editor.closeCallback(() => {
      tipsIsShow.value = false;
      drawType.value = '';
      drawIcon.value = '';
    });
    sqMarker.value.editor.drawer?.selectCallback((node: any) => {
      const id = node.getId?.();
      drawSelectId.value = id;
    });
    sqMarker.value.editor.drawer?.cancelSelectCallback(() => {
      drawSelectId.value = '';
    });
    sqMarker.value.label.attach({
      update: (labels?: any) => {
        const data = sqMarker.value?.localJSONToRadingSoftwareJSON(labels);
        // 把 JSON 数据转成 Blob 对象
        const blob = new Blob([JSON.stringify(data)], { type: 'application/json' });
        // 如果需要一个文件名，可以用 File 构造器（部分浏览器支持）
        const file = new File([blob], 'data.json', { type: 'application/json' });
        console.log(file);
        // 构造 FormData
        const formData = new FormData();
        formData.append('file', file); // 字段名必须是 file，和后端一致
        props.slice?.saveAnnotation(formData);
      },
    });
    const data = await props.slice?.getAnnotation();
    if (!data) return;
    // 1. 解码 base64 为字符串
    const jsonStr = atob(data as string);
    // 2. 转换为 JSON 对象
    const jsonObj = JSON.parse(jsonStr);
    const markerData = sqMarker.value?.RadingSoftwareJSONTolocalJSON(
      props.imageBrower.viewer,
      jsonObj
    );
    sqMarker.value.editor.addMarker(markerData);
    sqMarker.value.label.setLabelsByLayer(sqMarker.value.editor._layer);
    console.log(markerData);

    markerData.forEach((item: any) => {
      markerList.value.push({
        labelName: item.attrs?.name,
        labelColor: item.attrs?.stroke,
        labelWidth: item.attrs?.strokeWidth,
        id: item.id,
        name: item.attrs?.name,
        icon: markerType.find((val: any) => val.type === item.attrs?.name)?.icon,
      });
    });
  });
</script>
<style lang="scss" scoped>
  .marker-item {
    &.active {
      border-color: rgba(var(--primary-6), 1);
    }
  }
</style>
