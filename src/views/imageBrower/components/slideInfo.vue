<template>
  <a-modal v-model:visible="visible">
    <template #title>
      <div class="text-[22px] font-bold">{{ $t('index.bhcJZPDk') }}</div>
    </template>
    <div class="p-[16px] text-[18px] bg-[#F4F7FD]">
      <div class="grid gap-2 grid-cols-2">
        <div class="col-span-2">
          <div class="font-bold">{{ $t('index.tAQWfPZ3') }}</div>
          <div>{{ getLastCharacter(props.slice?.slicePath + '', '/') }}</div>
        </div>
        <div>
          <div class="font-bold">{{ $t('index.Zyf4kprY') }}</div>
          <div>{{ props.slice?.sliceInfo?.extraInfo?.scanTime }}S</div>
        </div>
        <!-- <div>
              <div class="font-bold">{{ $t('index.3Qiz5rFF') }}</div>
              <div>-</div>
            </div> -->
        <div>
          <div class="font-bold">{{ $t('index.x7jTsk5b') }}</div>
          <div>{{ props.slice?.sliceInfo?.hierarchy }}</div>
        </div>
        <!-- <div>
              <div class="font-bold">{{ $t('index.rS2yimdk') }}</div>
              <div>-</div>
            </div> -->
        <div>
          <div class="font-bold">{{ $t('index.sxXfXaFk') }}</div>
          <div>{{ convertToBestUnit(props.slice?.sliceInfo?.fileSize) }}</div>
        </div>
        <div>
          <div class="font-bold">{{ $t('index.YXJ4nKMG') }}</div>
          <div>{{ props.slice?.sliceInfo?.rate }}X</div>
        </div>
        <!-- <div>
              <div class="font-bold">{{ $t('index.HT3NPPZM') }}</div>
              <div>-</div>
            </div> -->
        <div>
          <div class="font-bold">{{ $t('index.eAePapsw') }}</div>
          <div>{{ props.slice?.sliceInfo?.extraInfo?.sliceFmt }}</div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="flex justify-center">
        <a-button class="w-[222px] h-[54px] text-[18px]" type="primary" @click="visible = false">
          {{ $t('index.REhitKma') }}
        </a-button>
      </div>
    </template>
  </a-modal>
</template>
<script setup lang="ts">
  import { convertToBestUnit, getLastCharacter, sdpcinfo } from '@/utils/sdpc';
  const props = defineProps({
    slice: {
      type: Object as PropType<sdpcinfo | undefined>,
      required: true,
    },
  });
  const visible = defineModel<boolean>('visible');
</script>
<style lang="scss" scoped></style>
