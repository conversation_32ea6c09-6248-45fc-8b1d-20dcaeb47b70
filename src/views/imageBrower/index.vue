<template>
  <a-spin :loading="sliceLoading" :tip="$t('index.EB6Aa4cz')" class="w-screen h-screen">
    <div
      class="w-screen h-screen relative bg-color-bg-1 select-none"
      ref="pathologyViewer"
      id="pathologyViewer"
    >
      <!-- 顶部工具 start  -->
      <TopTool
        v-if="imageBrower?.viewer"
        v-model:visible="visible"
        :slice="slice"
        :image-brower="imageBrower"
      />
      <!-- 顶部工具 end -->

      <div
        class="w-full h-full relative overflow-hidden"
        id="image-broswer-box"
        :data-id="slice?.sliceServerId"
      >
        <!-- openseadragon查看器 -->
        <div id="image-broswer" v-show="slice?.sliceServerId" :data-id="slice?.sliceServerId">
          <div id="image-broswer-main"></div>
          <div class="absolute top-[92px] left-[15px]" id="image-broswer-navigator"></div>
        </div>
        <!-- 加载中 -->
        <div class="w-full h-full" v-show="!slice?.sliceServerId">
          <a-spin v-show="slice?.loadingServerId" class="bg-color-bg-1 w-full h-full"></a-spin>
          <div
            class="w-full h-full flex flex-col justify-center items-center"
            v-show="!slice?.loadingServerId"
          >
            <div>
              <img class="h-[100px] object-contain" src="@/assets/images/sliceLoaderr.png" />
            </div>
            <div class="text-zinc-400 font-bold mt-2">
              <span>{{ $t('index.tyKFPa66') }}</span>
            </div>
          </div>
        </div>

        <div class="scaleRuler absolute bottom-6 left-5 z-[101]" v-show="slice?.sliceServerId">
          <div class="text-center absolute bottom-1 left-1/2 -translate-x-1/2 text-text-color">
            {{ ScaleRuler.scales }}
          </div>
          <div
            class="h-4 border-b border-r border-l border-text-color"
            :style="{ width: ScaleRuler.scalesWidth + 'px' }"
          ></div>
        </div>
        <div
          class="min-w-max h-[98px] absolute bottom-3 z-[101] flex flex-row"
          :style="{ right: rightWidth }"
        >
          <a-image v-if="slice?.label" fit="contain" height="98" :src="slice?.label" />
          <a-image v-if="slice?.macrograph" fit="contain" height="98" :src="slice?.macrograph" />
        </div>
      </div>
    </div>

    <!-- 明场颜色调节 start -->
    <CorrectionColor v-if="imageBrower?.viewer" :slice="slice" :image-brower="imageBrower" />
    <!-- 明场颜色调节 end -->

    <!-- 切片信息 start -->
    <SlideInfo v-if="slice" v-model:visible="visible" :slice="slice" />
    <!-- 切片信息 end -->

    <!-- 标注  start-->
    <Marker v-if="imageBrower?.viewer" :imageBrower="imageBrower" :slice="slice" />
    <!-- 标注  end-->
  </a-spin>
</template>

<script lang="ts" setup>
  import { Brower } from 'pathological-viewer';
  import sdpc, { sdpcinfo } from '@/utils/sdpc';
  import useLocale from '@/hooks/locale';
  import Marker from './components/marker/marker.vue';
  import CorrectionColor from './components/correctionColor.vue';
  import SlideInfo from './components/slideInfo.vue';
  import TopTool from './components/topTool.vue';
  import useStore from '@/store';
  const { changeLocale } = useLocale();
  const { marker } = useStore();
  const route = useRoute();
  const visible = ref(false);
  const slice = ref<sdpcinfo>(); //当前展开切片 切片资源信息
  const imageBrower = ref<Brower>(); //openseadragon对象
  const pathologyViewer = ref();

  //比例尺的值及长度等
  const ScaleRuler = computed(() => {
    if (!imageBrower.value) return { scales: 0, scalesWidth: 0 };
    return imageBrower.value!.scale;
  });

  //右侧宽度
  const rightWidth = computed(() => {
    const screenHeight = document.documentElement.clientHeight;
    if (marker.markerOpen && screenHeight < 850) return '86px';
    return '10px';
  });

  //防止频繁点击切换切片，等待切片完成加载后再进行切换
  const sliceLoading = ref(false);
  const initSlice = async (url: string) => {
    try {
      slice.value = new sdpc({
        slicePath: url,
      }) as sdpcinfo;
      //如遇切片解析失败，可以继续切换切片
      await slice.value?.init?.().catch(() => {
        setTimeout(() => {
          sliceLoading.value = false;
        }, 1000);
      });
      if (!slice.value?.sliceServerId) return;
      let colorMd5 = '';
      imageBrower.value = new Brower(slice.value!, 'image-broswer', colorMd5, {
        imageLoaderLimit: 8,
        animationTime: 1,
        preserveImageSizeOnResize: true,
        constrainDuringPan: false,
        zoomPerSecond: 2,
        autoHideControls: false,
      });
      imageBrower.value.init().finally(() => {
        setTimeout(() => {
          sliceLoading.value = false;
          document
            .getElementById('image-broswer-navigator')
            ?.getElementsByClassName('openseadragon-container')[1]
            ?.remove();
        }, 500);
      });
    } catch (err) {
      console.log(err, 1234444);
    }
  };
  watch(
    () => route.query.url,
    async (n) => {
      if (n) {
        //重置数据值
        if (slice.value?.sliceServerId && imageBrower.value?.viewer) {
          await imageBrower.value?.destroyed();
          slice.value?.destroy?.();
        }
        sliceLoading.value = true;
        initSlice(n as string);
      }
    }
  );

  onMounted(async () => {
    const { url, language } = route.query;
    if (language) {
      changeLocale(language + '');
    }
    //加载选择的切片信息
    if (url) {
      sliceLoading.value = true;
      initSlice(url as string);
    }
  });

  onUnmounted(() => {
    imageBrower.value?.destroyed();
  });
</script>

<style lang="scss" scoped>
  #image-broswer,
  #ruler {
    height: calc(100%);
    width: calc(100%);
    position: absolute;
    overflow: hidden;
  }
  #image-broswer-main {
    width: 100%;
    height: 100%;
  }
  #image-broswer-navigator {
    width: 130px;
    height: 130px;
    z-index: 2;
  }
</style>
<style lang="scss">
  .arco-modal-footer {
    border-top: none;
    padding-top: 0;
    padding-bottom: 24px;
  }
  .arco-modal-close-btn {
    font-size: 22px;
  }
  .arco-modal-header .arco-modal-title-align-center {
    justify-content: start;
  }
</style>
