<script setup lang="ts">
  import enUS from '@arco-design/web-vue/es/locale/lang/en-us';
  import zhCN from '@arco-design/web-vue/es/locale/lang/zh-cn';
  import zhTW from '@arco-design/web-vue/es/locale/lang/zh-tw';
  import useLocale from '@/hooks/locale';
  const { currentLocale } = useLocale();
  const locale = computed(() => {
    switch (currentLocale.value) {
      case 'zh-CN':
        return zhCN;
      case 'en-US':
        return enUS;
      case 'zh-TW':
        return zhTW;
      default:
        return enUS;
    }
  });
</script>

<template>
  <a-config-provider :locale="locale">
    <a-layout class="min-h-screen">
      <slot></slot>
    </a-layout>
  </a-config-provider>
</template>

<style></style>
