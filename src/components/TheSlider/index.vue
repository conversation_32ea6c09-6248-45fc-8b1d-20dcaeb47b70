<template>
  <div
    class="relative w-full h-3 rounded-[7px] cursor-pointer bg-theme-color-2"
    @mousedown="startDrag"
    @touchstart="startDrag"
  >
    <!-- 滑块轨道 -->
    <div class="relative w-full h-full">
      <div
        class="absolute h-full bg-theme-color rounded-[12px]"
        :style="{ width: progressPercent + '%' }"
      ></div>
    </div>
    <!-- 滑块按钮 -->
    <div
      class="
        absolute
        top-1/2
        w-[25px]
        h-[25px]
        bg-theme-color
        rounded-[50%]
        translate-y-[-50%] translate-x-[-50%]
        border-[3px] border-theme-color-2
        cursor-grab
      "
      :style="{ left: progressPercent + '%' }"
      @mousedown.prevent
      @touchstart.prevent
      @click="handleClick"
      @touchend="handleTouchEnd"
    ></div>
  </div>
</template>

<script lang="ts" setup>
  const props = defineProps({
    modelValue: {
      type: Number,
      default: 0,
    },
    min: {
      type: Number,
      default: -100,
    },
    max: {
      type: Number,
      default: 100,
    },
    step: {
      type: Number,
      default: 1,
    },
    initial: {
      type: Number,
      default: 0,
    },
  });
  const emit = defineEmits(['update:modelValue', 'change']);
  const progress = ref(0);
  let isDragging = false;
  let sliderWidth = 0;
  let sliderOffsetLeft = 0;
  let lastTouchTime = 0;
  const DOUBLE_TAP_DELAY = 300; // 300ms 内判定为双击
  // 计算滑块位置百分比
  const progressPercent = computed(() => {
    return ((props.modelValue - props.min) / (props.max - props.min)) * 100;
  });

  // 根据像素值计算滑块值
  const calculateValue = (offsetX: number) => {
    const totalRange = props.max - props.min;
    if (totalRange === 0) return props.min; // 避免除零错误

    const value = props.min + (offsetX / sliderWidth) * totalRange;
    let steppedValue;

    if (Number.isInteger(props.step)) {
      steppedValue = Math.round(value / props.step) * props.step;
    } else {
      const stepDecimalPlaces = props.step.toString().split('.')[1]?.length || 0;
      const factor = Math.pow(10, stepDecimalPlaces);
      steppedValue = Math.round(value * factor) / factor;
      steppedValue = Math.round(steppedValue / props.step) * props.step;
      steppedValue = Math.round(steppedValue * factor) / factor;
    }
    return Math.min(Math.max(steppedValue, props.min), props.max);
  };

  const startDrag = (event: MouseEvent | TouchEvent) => {
    isDragging = true;
    const slider = event.currentTarget as HTMLElement;
    sliderWidth = slider.getBoundingClientRect().width;
    sliderOffsetLeft = slider.getBoundingClientRect().left;

    document.addEventListener('mousemove', onDrag);
    document.addEventListener('touchmove', onDrag);
    document.addEventListener('mouseup', stopDrag);
    document.addEventListener('touchend', stopDrag);

    onDrag(event); // 初次拖动时更新值
  };

  const onDrag = (event: MouseEvent | TouchEvent) => {
    if (!isDragging) return;

    let clientX: number;
    if (event.type.startsWith('touch')) {
      if ((event as TouchEvent).touches.length !== 1) return;
      clientX = (event as TouchEvent).touches[0].clientX;
    } else {
      clientX = (event as MouseEvent).clientX;
    }

    const offsetX = Math.min(Math.max(clientX - sliderOffsetLeft, 0), sliderWidth);
    const newValue = calculateValue(offsetX);
    console.log(newValue);

    emit('update:modelValue', newValue);
  };
  const resetToInitial = () => {
    console.log('双击了');

    emit('update:modelValue', props.initial);
    emit('change', props.initial);
  };
  // 鼠标点击也支持
  const handleClick = (event: MouseEvent) => {
    if (event.detail === 2) {
      resetToInitial();
    }
  };

  const handleTouchEnd = () => {
    const now = Date.now();
    if (now - lastTouchTime < DOUBLE_TAP_DELAY) {
      resetToInitial();
      lastTouchTime = 0; // 重置防止连续误触
    } else {
      lastTouchTime = now;
    }
  };
  const stopDrag = () => {
    isDragging = false;
    document.removeEventListener('mousemove', onDrag);
    document.removeEventListener('touchmove', onDrag);
    document.removeEventListener('mouseup', stopDrag);
    document.removeEventListener('touchend', stopDrag);
    // 触发 change 事件
    emit('change', props.modelValue);
  };

  // 当 modelValue 更新时，修正 progress
  watch(
    () => props.modelValue,
    (newValue) => {
      progress.value = newValue;
    }
  );
</script>
