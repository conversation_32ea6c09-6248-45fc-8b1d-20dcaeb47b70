// src/components/SvgIcon/SvgIcon.vue
<template>
  <svg :class="svgClass" :width="props.size" :height="props.size" aria-hidden="true">
    <use :xlink:href="iconClassName" :fill="color" :width="props.size" :height="props.size" />
  </svg>
</template>
<script setup lang="ts">
  import { computed } from 'vue';
  const props = defineProps({
    iconName: {
      type: String as any,
      required: true,
    },
    className: {
      type: String,
      default: '',
    },
    color: {
      type: String,
      default: '',
    },
    size: {
      type: Number,
      default: 14,
    },
  });
  // 图标在 iconfont 中的名字
  const iconClassName = computed(() => {
    return `#${props.iconName}`;
  });
  // 给图标添加上类名
  const svgClass = computed(() => {
    if (props.className) {
      return `svg-icon ${props.className}`;
    }
    return 'svg-icon';
  });
</script>
<style scoped>
  .svg-icon {
    position: relative;
    fill: currentColor;
    vertical-align: -2px;
  }
</style>
