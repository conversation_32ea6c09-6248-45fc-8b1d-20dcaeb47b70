<script setup lang="ts">
  import useStore from '@/store';
  const { configuration } = useStore();
  // configuration.applySystemTheme();
  // configuration.refreshThemeType();
  configuration.changeThemeColor();
  // if (configuration.configuration.colorWeak) {
  //   document.body.style.filter = 'invert(80%)';
  // }
</script>

<template>
  <Configprovider>
    <router-view />
  </Configprovider>
</template>

<style lang="scss" scoped></style>
