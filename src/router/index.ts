import { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router';
import { App } from 'vue';
export const BaseRoutes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'ImageBrower',
    component: () => import('@/views/imageBrower/index.vue'),
  },
  {
    path: '/:path(.*)',
    name: '404',
    component: () => import('@/views/errer/index.vue'),
  },
];
const router = createRouter({
  history: createWebHashHistory(),
  routes: BaseRoutes,
});
export function mountRouter(app: App) {
  app.use(router);
}
export default router;
