import { defineStore } from 'pinia';
export default defineStore('marker', {
  state: () => ({
    /**线条宽度 */
    lineWidth: 2,

    /**线条颜色 */
    lineColor: '#FF0000',

    /** 标注工具是否打开*/
    markerOpen: false,

    /**颜色调节是否显示 */
    colorAdjustShow: false,

    markerIsOpen: false,
  }),

  //按需配置数据持久化 这里指定变量num保持持久化
  persist: {
    //默认名称为当前store唯一标识 这里即home
    key: 'inset-marker',
    //默认localStorage 本地储存
    //这里建议临时储存sessionStorage 也可写成window.sessionStorage
    storage: localStorage,
    //默认当前store里的所有变量都持久化
    paths: ['lineWidth', 'lineColor'],
  },
});
