import { defineStore } from 'pinia';
import { generate, getRgbStr } from '@arco-design/color';
export default defineStore('configuration', {
  state: () => ({
    /**设置配置对象 */
    configuration: {
      /**主题色 */
      themeColor: '#20BCD5',
      /**dark or light */
      themeType: 'light' as 'dark' | 'light',
      /**菜单宽度 */
      menuWidth: 200,
      /**是否第一次进入系统 */
      isFirstTime: true,
      /**设置抽屉是否打开 */
      isDrawerOpen: false,
      /**导航栏 */
      navbar: true,
      /**菜单栏 */
      menu: true,
      /**色弱模式 */
      colorWeak: false,
      /**顶部菜单栏 */
      topMenu: false,
      /**菜单是否展开 */
      menuCollapse: false,
      /**折叠按钮是否显示 */
      showCollapseButton: true,
    },
  }),
  actions: {
    /**暗黑切换 */
    toggleDarkMode() {
      if (this.configuration.themeType === 'light') {
        document.body.setAttribute('arco-theme', 'dark');
        this.configuration.themeType = 'dark';
      } else {
        document.body.removeAttribute('arco-theme');
        this.configuration.themeType = 'light';
      }
    },
    /**第一次进入系统，跟随系统主题色  */
    applySystemTheme() {
      if (!this.configuration.isFirstTime) return;
      if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
        this.configuration.themeType = 'dark';
        document.body.setAttribute('arco-theme', 'dark');
      } else if (window.matchMedia('(prefers-color-scheme: light)').matches) {
        this.configuration.themeType = 'light';
        document.body.removeAttribute('arco-theme');
      }
      this.configuration.isFirstTime = false;
      if (
        navigator.userAgent.match(
          /Android|BlackBerry|iPhone|iPad|iPod|Opera Mini|IEMobile|WPDesktop/i
        ) ||
        this.isIpadFun()
      ) {
        this.configuration.menuCollapse = true;
        this.configuration.showCollapseButton = false;
      }
    },
    //刷新重新设置themeType
    refreshThemeType() {
      if (this.configuration.themeType === 'dark') {
        document.body.setAttribute('arco-theme', 'dark');
      } else {
        document.body.removeAttribute('arco-theme');
      }
    },
    /**更换主题色 */
    changeThemeColor() {
      const theme = document.querySelector('body')?.getAttribute('arco-theme') || 'light';
      const newList = generate(this.configuration.themeColor, {
        list: true,
        dark: theme === 'dark',
      });
      newList.forEach((l: any, index: number) => {
        const rgbStr = getRgbStr(l);
        document.body.style.setProperty(`--arcoblue-${index + 1}`, rgbStr);
      });
    },
    isIpadFun() {
      return (
        navigator.userAgent.match(/(iPad)/) ||
        (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1)
      );
    },
  },

  //按需配置数据持久化 这里指定变量num保持持久化
  persist: {
    //默认名称为当前store唯一标识 这里即home
    key: 'inset-configuration',
    //默认localStorage 本地储存
    //这里建议临时储存sessionStorage 也可写成window.sessionStorage
    storage: localStorage,
    //默认当前store里的所有变量都持久化
    paths: ['configuration'],
  },
});
