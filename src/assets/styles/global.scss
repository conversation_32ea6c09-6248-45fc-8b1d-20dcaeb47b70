::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 5px; /*高宽分别对应横竖滚动条的尺寸*/
  height: 5px;
}
::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(97, 184, 179, 0.1);
  background: rgb(var(--primary-6));
}
::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  box-shadow: inset 0 0 5px rgba(87, 175, 187, 0.1);
  border-radius: 10px;
  background: #ededed;
}
.g-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.g-ellipsis1 {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden; //溢出内容隐藏
  text-overflow: ellipsis; //文本溢出部分用省略号表示
  display: -webkit-box; //特别显示模式
  -webkit-line-clamp: 2; //行数
  line-clamp: 2;
  -webkit-box-orient: vertical; //盒子中内容竖直排列
}

.g-ellipsis2 {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden; //溢出内容隐藏
  text-overflow: ellipsis; //文本溢出部分用省略号表示
  display: -webkit-box; //特别显示模式
  -webkit-line-clamp: 5; //行数
  line-clamp: 5;
  -webkit-box-orient: vertical; //盒子中内容竖直排列
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

html,
body {
  width: 100%;
  height: 100%;
  font-size: 14px;
  background-color: var(--color-bg-1);
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
}

.info-modal-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

[id$='-navigator'] {
  position: absolute !important;
  border: 1px solid #e8e8e8;
  background: transparent;
}

#tooltipDirective {
  position: absolute;
  padding: 5px;
  color: #fff;
  border-radius: 4px;
  pointer-events: none;
  font-size: 12px;
  line-height: 16px;
}
#tooltipDirective::before {
  content: '';
  width: 4px;
  height: 4px;
  border: solid #1d2129;
  background-color: #1d2129;
  border-width: 1px 0 0 1px;
  transform: translateX(-50%) rotate(45deg);
  position: absolute;
  top: -2px;
  left: 50%;
}

.border-color-1 {
  border: 1px solid var(--color-neutral-2);
}
#image-broswer-navigator {
  width: 200px;
  height: 200px;
  z-index: 2;
}
.vc-input__label {
  color: var(--color-text-1) !important;
}

:root {
  --color-picker-bg: #ffffff;
  --color-picker-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
}

input[type='password']::-ms-reveal {
  display: none;
}

input[type='password']::-ms-clear {
  display: none;
}

input[type='password']::-o-clear {
  display: none;
}

:focus-visible {
  outline: 0;
}

.arco-image-preview-toolbar-action-content .arco-icon {
  width: 28px;
  height: 28px;
}
