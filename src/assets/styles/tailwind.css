@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .flex-c {
    @apply flex justify-center items-center;
  }
  .bg-details {
    @apply bg-cover bg-no-repeat bg-center;
  }
  .button-color {
    @apply h-[48px] leading-[48px] bg-[#F4F7FD] rounded-[4px];
  }
  .active-button {
    @apply active:bg-theme-color active:text-color-bg-1;
  }
  .pop-confirm {
    @apply absolute top-[42px] w-[263px] bg-white rounded-[4px] p-[11px] z-10 shadow-box-shadow-2;
  }
  .pop-confirm-s {
    @apply absolute  -top-[8px] w-0 h-0 border-x-[8px] border-x-transparent border-b-[8px] border-b-white drop-shadow-sm;
  }
}
.anticon svg {
  vertical-align: baseline;
}
