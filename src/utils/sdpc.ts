import { SdpcParser } from 'pathological-viewer';
import axios from 'axios';
export class sdpcinfo extends SdpcParser {
  label: string;
  macrograph: string;
  sliceInfo: any;
  slicePath: string;
  colorChange: any;
  sliceServerId: string;
  multiColorList: any[];
  colorList: any;
  loadingServerId: boolean;
  dcmColorChange: (colorList: any[]) => Promise<void>;
  multiDcmColor: () => void;
  svsColor: () => void;
  sdpcColor: () => void;
  dcmColor: () => void;
  saveAnnotation: (data: any) => Promise<void>;
  getAnnotation: () => Promise<string>;
}

export async function getImage(path: string): Promise<string> {
  const res = await axios.get(path, {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Content-Type': 'application/octet-stream',
      // "Authorization": backendToken
    },
    responseType: 'blob',
  });
  return res.data.size ? path : '';
}
//后台插件解码
export default class sdpc implements sdpcinfo {
  sdpc: any;
  BackEndUrl: string; //后端服务地址
  readingStore: string; //后台切片获取需要的地址
  bucketName: string; //存储桶名
  hasPlug: boolean;
  sliceServerId: string;
  plugStorageUploaUrl: string;
  slicePath: string;
  thumbnail: string;
  label: string;
  macrograph: string;
  sliceInfo: any;
  multiColorList: any[] = [];
  colorList: any;
  loadingServerId = false;
  resetServerId: any;
  channal: any;
  timeCache: number;
  constructor(conifg: { slicePath: string }) {
    // this.BackEndUrl = 'http://192.168.20.44:57013';
    this.BackEndUrl = '';
    this.slicePath = conifg.slicePath;
  }
  async init() {
    await this.getSliceID(this.slicePath);
    this.timeCache = new Date().getTime();
  }
  //获取基本的sliceID
  async getSliceID(urlPath: string) {
    try {
      this.loadingServerId = true;

      const urlId = encodeURIComponent(urlPath);
      this.hasPlug = true;
      const url = `${this.BackEndUrl}/api/SliceService/image-slices/open-local-disk/${urlId}`;

      return new Promise(async (resolve, reject) => {
        try {
          await axios({
            url,
            headers: {
              'Access-Control-Allow-Origin': '*',
              'Access-Control-Allow-Headers': 'X-Requested-With',
            },
            // data:{
            //     path :urlPath
            // },
            method: 'post',
          }).then(async (res: any) => {
            this.sliceServerId = res.data;
            this.thumbnail = await getImage(
              `${this.BackEndUrl}/api/SliceService/image-slices/${res.data}/thumbnail`
            );
            this.label = await getImage(
              `${this.BackEndUrl}/api/SliceService/image-slices/${res.data}/label`
            );
            this.macrograph = await getImage(
              `${this.BackEndUrl}/api/SliceService/image-slices/${res.data}/macrograph`
            );
            this.ifServerIdIsExit();
            resolve(true);
          });
        } catch (err) {
          this.loadingServerId = false;
          reject(err);
          console.warn(err, '切片存在问题， 无法获取切片的ID');
        }
      });
    } catch (err) {
      this.sliceServerId = '';
      console.error(err);
      return false;
    }
  }
  // 根据ID获取信息
  async getPicHead() {
    try {
      let data;
      let url;
      if (this.hasPlug) {
        url = `${this.BackEndUrl}/api/SliceService/image-slices/${this.sliceServerId}/info`;
      } else {
        url = `${this.BackEndUrl}/api/SliceService/image-slices/${this.sliceServerId}/info`;
      }
      await axios
        .get(url, {
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'X-Requested-With',
          },
        })
        .then((res: any) => {
          data = res.data;
        })
        .catch((err: any) => {
          this.exitSliceId();
        });
      this.sliceInfo = data;
      return data;
    } catch (err) {
      console.error(err);
      return false;
    }
  }
  // 根据id获取荧光的信息
  async multiDcmColor() {
    await axios
      .get(
        `${this.BackEndUrl}/api/SliceService/image-slices/${this.sliceServerId}/multi-dcm-color-info`
      )
      .then((res: any) => {
        this.multiColorList = res.data.channelList
          .filter((item: any) => item.id)
          .map((item: any) => {
            return { ...item, selected: true, popshow: false };
          });
      })
      .catch((err: any) => {
        this.exitSliceId();
      });
  }
  // 根据id获取明场的信息
  async svsColor() {
    await axios
      .get(`${this.BackEndUrl}/api/SliceService/image-slices/${this.sliceServerId}/svs-color-info`)
      .then((res: any) => {
        this.colorList = res.data;
      })
      .catch((err: any) => {
        this.exitSliceId();
      });
  }
  // 根据id获取明场的信息
  async sdpcColor() {
    await axios
      .get(`${this.BackEndUrl}/api/SliceService/image-slices/${this.sliceServerId}/sdpc-color-info`)
      .then((res: any) => {
        this.colorList = res.data;
      })
      .catch((err: any) => {
        this.exitSliceId();
      });
  }
  // 根据id获取明场的信息
  async dcmColor() {
    await axios
      .get(
        `${this.BackEndUrl}/api/SliceService/image-slices/${this.sliceServerId}/dcm-bright-field-color-info`
      )
      .then((res: any) => {
        this.colorList = res.data;
      })
      .catch((err: any) => {
        this.exitSliceId();
      });
  }
  /**
   * 保存标注
   * fromData 标注数据
   */
  async saveAnnotation(formData: any) {
    await axios.post(
      `${this.BackEndUrl}/api/SliceService/image-slices/${this.sliceServerId}/save-mask`,
      formData
    );
  }
  /**
   * 获取标注
   */
  async getAnnotation() {
    const res = await axios.get(
      `${this.BackEndUrl}/api/SliceService/image-slices/${this.sliceServerId}/get-mask`
    );
    return res.data;
  }

  //明场颜色调节
  async colorChange(colorList: number[]) {
    try {
      this.timeCache = new Date().getTime();
      if (
        !this.sliceServerId ||
        (this.sliceInfo.dcmExtraInfo && this.sliceInfo.dcmExtraInfo?.dcmType)
      )
        return;
      if (this.slicePath.match(/sdpc$/)) {
        await axios
          .put(
            `${this.BackEndUrl}/api/SliceService/image-slices/${this.sliceServerId}/sdpc-color-info`,
            {
              colorType: 1,
              red: colorList[0],
              green: colorList[1],
              blue: colorList[2],
              brightness: colorList[3],
              contrast: colorList[4],
              gamma: colorList[5],
            }
          )
          .then((res: any) => {
            console.log(res);
          });
      } else if (this.slicePath.match(/svs$/)) {
        await axios
          .put(
            `${this.BackEndUrl}/api/SliceService/image-slices/${this.sliceServerId}/svs-color-info`,
            {
              colorType: 1,
              red: colorList[0],
              green: colorList[1],
              blue: colorList[2],
              brightness: colorList[3],
              contrast: colorList[4],
              gamma: colorList[5],
            }
          )
          .then((res: any) => {
            console.log(res);
          });
      } else {
        await axios.put(
          `${this.BackEndUrl}/api/SliceService/image-slices/${this.sliceServerId}/set-dcm-bright-field-color-info`,
          {
            colorType: 1,
            red: colorList[0],
            green: colorList[1],
            blue: colorList[2],
            brightness: colorList[3],
            contrast: colorList[4],
            gamma: colorList[5],
          }
        );
      }
      if (this.resetServerId) {
        this.channal = colorList;
        this.ifServerIdIsExit();
      }
    } catch (err) {
      throw err;
    }
  }
  //荧光颜色调节
  async dcmColorChange(colorList: any[]) {
    this.multiColorList = colorList;
    this.timeCache = new Date().getTime();
    const channelList = colorList.filter((item) => item.selected);
    await axios.put(
      `${this.BackEndUrl}/api/SliceService/image-slices/${this.sliceServerId}/multi-dcm-color-info`,
      {
        channelList,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'X-Requested-With',
        },
      }
    );
    if (this.resetServerId) {
      this.channal = channelList;
      this.ifServerIdIsExit();
    }
  }

  getTile(level: number, x: number, y: number, ColorMd5?: string) {
    return `${this.BackEndUrl}/api/SliceService/image-slices/${this.sliceServerId}/tile?Level=${level}&X=${x}&Y=${y}&ColorMd5=${ColorMd5}`;
  }

  /**
   * 超时重置切片服务id
   */
  ifServerIdIsExit() {
    if (this.resetServerId) {
      clearTimeout(this.resetServerId);
    }
    this.resetServerId = setTimeout(async () => {
      await this.getSliceID(this.slicePath);
      //更新一下颜色调节
      if (this.channal) {
        this.sliceInfo.dcmExtraInfo?.dcmType
          ? this.dcmColorChange(this.channal)
          : this.colorChange(this.channal);
      }
    }, 20 * 60 * 1000);
  }

  private async exitSliceId() {
    const isFlag = await axios.get(
      `${this.BackEndUrl}/api/SliceService/image-slices/${this.sliceServerId}/is-exist`
    );
    if (!isFlag) {
      await this.getSliceID(this.slicePath);
    } else {
      console.log('切片ID存在，当无法执行当前任务');
    }
  }

  async destroy() {
    if (this.resetServerId) {
      clearTimeout(this.resetServerId);
    }
    return true;
  }
}

export function convertToBestUnit(bytes: number) {
  const size = 1024;
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let index = 0;

  while (bytes >= size && index < units.length - 1) {
    bytes /= size;
    index++;
  }

  return `${bytes?.toFixed(2)} ${units[index]}`;
}

/**
 * 获取字符串最后/后面的字符
 */
export function getLastCharacter(str: string, separator: string): string {
  if (str === 'undefined') return '';
  const lastIndex = str.lastIndexOf(separator);
  if (lastIndex !== -1) {
    return str.substring(lastIndex + 1);
  }
  return str;
}
