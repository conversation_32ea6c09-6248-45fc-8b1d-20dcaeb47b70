import { createApp } from 'vue';
import App from './App.vue';
import '@arco-design/web-vue/es/message/style/css.js';
import '@arco-design/web-vue/es/notification/style/css.js';
import '@arco-design/web-vue/es/modal/style/css.js';
import '@/assets/styles/index.scss';
import '@/assets/iconfont/iconfont.js';
import { mountRouter } from '@/router';
import { createPinia } from 'pinia';
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';
import i18n from './locales/index';
const app = createApp(App);
const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);
mountRouter(app);
app.use(pinia);
app.use(i18n);
app.mount('#app');
