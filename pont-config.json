{"origins": [{"name": "Slices", "originType": "SwaggerV3", "originUrl": "http://**************:7023/swagger/Identity/swagger.json"}, {"name": "<PERSON><PERSON>", "originType": "SwaggerV3", "originUrl": "http://**************:7023/swagger/Auth/swagger.json"}, {"name": "SystemCase", "originType": "SwaggerV3", "originUrl": "http://**************:7023/swagger/Case/swagger.json"}, {"name": "System", "originType": "SwaggerV3", "originUrl": "http://**************:7023/swagger/System/swagger.json"}, {"name": "Identity", "originType": "SwaggerV3", "originUrl": "http://**************:7023/swagger/Identity/swagger.json"}, {"name": "ThirdPart", "originType": "SwaggerV3", "originUrl": "http://**************:7023/swagger/ThirdPart/swagger.json"}], "templatePath": "./pontTemplate", "outDir": "./src/services", "prettierConfig": {"printWidth": 100, "tabWidth": 2, "useTabs": false, "semi": true, "vueIndentScriptAndStyle": true, "singleQuote": true, "quoteProps": "as-needed", "bracketSpacing": true, "trailingComma": "es5", "jsxSingleQuote": false, "arrowParens": "always", "insertPragma": false, "requirePragma": false, "proseWrap": "never", "htmlWhitespaceSensitivity": "ignore", "endOfLine": "auto", "rangeStart": 0}, "transformPath": "./transformTemplate"}