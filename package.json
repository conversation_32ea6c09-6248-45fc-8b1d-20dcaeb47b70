{"$schema": "https://json.schemastore.org/jsconfig", "name": "sqray-implant-web-image", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve --mode development", "build-dev": "vue-cli-service build --mode production", "build": "vue-cli-service build --mode production", "build-pre-ann": "vue-cli-service build --mode preannouncement", "build-private": "vue-cli-service build --mode private", "lint": "eslint src", "fix": "eslint src --fix", "preinstall": "npx only-allow pnpm", "format": "prettier --write \"./**/*.{html,vue,js,ts,json,md}\" ", "prepare": "husky install", "commitlint": "commitlint --config commitlint.config.cjs -e -V", "api": "rimraf ./src/services && pont list"}, "dependencies": {"@arco-design/color": "^0.4.0", "@arco-design/web-vue": "^2.55.3", "@ckpack/vue-color": "^1.6.0", "axios": "^1.7.2", "md5": "2.3.0", "pathological-viewer": "0.1.13", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "sqray-filter-plugin": "^1.0.1", "sqray-marker": "1.0.319", "vue": "^3.4.37", "vue-i18n": "^9.13.1", "vue-router": "^4.0.3"}, "devDependencies": {"@commitlint/cli": "^18.6.1", "@commitlint/config-conventional": "^18.6.3", "@types/node": "^22.4.0", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-typescript": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/eslint-config-typescript": "^9.1.0", "babel-plugin-transform-remove-console": "^6.9.4", "compression-webpack-plugin": "^11.1.0", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.0.3", "husky": "^8.0.3", "pont-engine": "^1.6.3", "prettier": "^2.4.1", "sass": "^1.32.7", "sass-loader": "^12.0.0", "tailwindcss": "^3.4.4", "typescript": "~4.5.5", "unplugin-auto-import": "^0.16.7", "unplugin-vue-components": "^0.25.2", "webpack": "5.93.0", "webpack-bundle-analyzer": "^4.10.2"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended", "@vue/typescript/recommended", "plugin:prettier/recommended"], "parserOptions": {"ecmaVersion": 2020}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}