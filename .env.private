# 私有环境配置
NODE_ENV = 'production'

# 接口地址
VUE_APP_BASE_API = 'http://sq-slicelibrary:80/'

# 打包公共路径
VUE_APP_BASE_URL = '/miis/'
# 切片解码库地址
decodeSdpcCoreJsPath = '/hevc.js'
# 切片解码库地址
decodeSdpcCoreWasmPath = '/hevc.wasm'
# 阅片端口
VUE_APP_BACKEND = 'http://sq-slice:80'

VUE_APP_REMOTE_URL = "http://sq-slice:80"
VUE_APP_UPLOAD_PLUG_SERVICE_URL = "http://127.0.0.1:57012"
VUE_APP_UPLOAD_PLUG_SLICE_SERVICE_URL = "http://127.0.0.1:57013"
VUE_APP_PROJECT_ID = "miis"
VUE_APP_SUCCESS_URL = "api/Succes"
VUE_APP_AUTHORIZATION = "token"
VUE_APP_SERVICE_API = "http://sq-slice:80"
VUE_APP_WEB_SOCKET_URL = "ws://192.168.10.204:8810"
VUE_APP_READING_STORE = "ServerDiskFile"
