const { defineConfig } = require('@vue/cli-service');
const { ArcoResolver } = require('unplugin-vue-components/resolvers');
const ComponentsPlugin = require('unplugin-vue-components/webpack');
const webpack = require('webpack');
const CompressionPlugin = require('compression-webpack-plugin');
const productionGzipExtensions = /\.(js|css)(\?.*)?$/i;
// const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
const Timestamp = new Date().getTime();
module.exports = defineConfig({
  publicPath: '/',
  transpileDependencies: true,
  productionSourceMap: false,
  configureWebpack: {
    /*  */
    plugins: [
      ComponentsPlugin({
        resolvers: [ArcoResolver({ sideEffect: true })],
      }),
      require('unplugin-auto-import/webpack')({
        // 自动导入 Vue 相关函数，如：ref, reactive, toRef 等
        imports: ['vue', 'vue-router', 'pinia'],
        dts: 'src/auto-import.d.ts',
        eslintrc: {
          enabled: true, // <-- this
        },
      }),
      // 定义全局变量
      new webpack.DefinePlugin({
        'process.env.VUE_APP_VERSION': JSON.stringify(Timestamp),
      }),
      /**依赖分析 */
      // new BundleAnalyzerPlugin({ openAnalyzer: false }),
    ],
    externals: {
      SlideBank: 'SlideBank',
    },

    output: {
      filename: `js/[name].${Timestamp}.js`,
      chunkFilename: `js/[name].${Timestamp}.js`,
    },
  },

  chainWebpack: (config) => {
    config.plugin('define').tap((definitions) => {
      Object.assign(definitions[0], {
        __VUE_OPTIONS_API__: 'true',
        __VUE_PROD_DEVTOOLS__: 'false',
        __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: 'false',
      });
      return definitions;
    });
    // config.plugin('compressionPlugin').use(
    //   new CompressionPlugin({
    //     algorithm: 'gzip',
    //     test: productionGzipExtensions,
    //     threshold: 10240,
    //     deleteOriginalAssets: false,
    //   })
    // );
  },
  devServer: {
    client: {
      overlay: false,
    },
    open: true,
    port: 9527,
  },
});
