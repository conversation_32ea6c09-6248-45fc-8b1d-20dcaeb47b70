/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./index.html', './src/**/*.{vue,js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        'theme-color': 'rgb(var(--primary-6))',
        'theme-color/50': 'rgba(var(--primary-6), 0.5)',
        'theme-color-2': 'var(--color-primary-light-2)',
        'text-color': 'var(--color-text-1)',
        'color-text-1': 'var(--color-text-1)',
        'color-text-2': 'var(--color-text-2)',
        'color-fill-2': 'var(--color-fill-2)', //输入框默认背景色
        'card-word': '#7B7D83',
        'color-bg-1': 'var(--color-bg-1)',
        'color-bg-1/80': 'rgba(var(--color-bg-1), 0.8)', //#ffffff80
        'color-bg-2': 'var(--color-bg-2)', // 白色背景
        'color-neutral-2': 'var(--color-neutral-2)', // 灰色背景
        'color-neutral-3': 'var(--color-neutral-3)', // 灰色边框颜色
        'color-bg-2': 'var(--color-bg-2)',
        'gray-2': 'rgb(var(--gray-2))', // 灰色
        'gray-3': '#F2F3F5',
        'gray-4': '#D8D8D8',
      },
      screens: {
        '3xl': '2080px',
      },
      margin: {
        'y-auto': 'auto 0',
        'x-auto': '0 auto',
      },
      spacing: {
        75: '22rem' /*300px*/,
        84: '21rem' /*336px*/,
        88: '22rem' /*352px*/,
        136: '33.875rem' /*542px*/,
        144: '36rem' /*576px*/,
      },
      boxShadow: {
        'box-shadow': '0px 4px 10px 0px rgba(0,0,0,0.15);',
        'box-shadow-1': 'inset 0px 0px 4px 0px rgba(0,0,0,0.25);',
        'box-shadow-2': '0px 0px 10px 0px rgba(0,0,0,0.3);',
      },
    },
  },
  plugins: [],
};
